"""
Example usage of the Knowledge Base Bot for cattle farming documents.

This script demonstrates how to initialize, configure, and use the knowledge base bot
for document ingestion and query processing.
"""

import asyncio
import os
from pathlib import Path
from typing import List

from .knowledge_base import KnowledgeBaseBot
from .config import KnowledgeBaseBotConfig
from .utils import setup_logging


async def main():
    """
    Main example function demonstrating knowledge base bot usage.
    """
    # Setup logging
    setup_logging(log_level="INFO", log_file="knowledge_base_bot.log")
    
    # Load configuration from environment variables
    config = KnowledgeBaseBotConfig.from_env()
    
    # Initialize the knowledge base bot
    bot = KnowledgeBaseBot(config)
    
    try:
        # Perform health check
        print("Performing health check...")
        health_status = await bot.health_check()
        print(f"Health check result: {health_status['overall']}")
        
        if not health_status['overall']:
            print("Health check failed. Please check configuration and dependencies.")
            return
        
        # Example 1: Ingest a single document
        print("\n=== Example 1: Single Document Ingestion ===")
        document_path = "path/to/your/cattle_farming_guide.pdf"
        
        if Path(document_path).exists():
            metadata = {
                "document_type": "guide",
                "topic": "cattle_farming",
                "author": "Agricultural Extension Service"
            }
            
            success = await bot.ingest_document(document_path, metadata)
            print(f"Document ingestion successful: {success}")
        else:
            print(f"Document not found: {document_path}")
        
        # Example 2: Ingest multiple documents
        print("\n=== Example 2: Multiple Document Ingestion ===")
        document_paths = [
            "path/to/cattle_health_manual.pdf",
            "path/to/breeding_guidelines.docx",
            "path/to/pasture_management.pdf"
        ]
        
        # Filter existing documents
        existing_docs = [path for path in document_paths if Path(path).exists()]
        
        if existing_docs:
            metadata_list = [
                {"document_type": "manual", "topic": "health"},
                {"document_type": "guidelines", "topic": "breeding"},
                {"document_type": "guide", "topic": "pasture"}
            ][:len(existing_docs)]
            
            results = await bot.ingest_documents(existing_docs, metadata_list)
            print(f"Ingestion results: {results}")
        else:
            print("No documents found for batch ingestion")
        
        # Example 3: Query processing
        print("\n=== Example 3: Query Processing ===")
        
        # Sample queries
        queries = [
            "How do I care for newborn calves?",
            "What are the signs of mastitis in dairy cows?",
            "When should I wean bull calves?",
            "How to manage pasture rotation for cattle?"
        ]
        
        # Conversation history (simulated)
        conversation_history = [
            {"role": "user", "content": "I'm new to cattle farming"},
            {"role": "assistant", "content": "Welcome! I'd be happy to help you with cattle farming questions."}
        ]
        
        for query in queries:
            print(f"\nQuery: {query}")
            
            response = await bot.query(query, conversation_history)
            
            if response.get("success", False):
                print(f"Response: {response['response'][:200]}...")
                print(f"Confidence: {response['confidence']:.2f}")
                print(f"Sources used: {len(response['sources'])}")
                print(f"Processing time: {response['processing_time']:.2f}s")
                
                # Add to conversation history
                conversation_history.append({"role": "user", "content": query})
                conversation_history.append({"role": "assistant", "content": response['response']})
                
                # Keep only last 10 messages
                if len(conversation_history) > 10:
                    conversation_history = conversation_history[-10:]
            else:
                print(f"Query failed: {response.get('error', 'Unknown error')}")
        
        # Example 4: Get collection information
        print("\n=== Example 4: Collection Information ===")
        collection_info = await bot.get_collection_info()
        if collection_info:
            print(f"Collection info: {collection_info}")
        else:
            print("Failed to get collection information")
        
        # Example 5: Delete a document (optional)
        print("\n=== Example 5: Document Deletion ===")
        # Uncomment to test document deletion
        # delete_success = await bot.delete_document("path/to/document_to_delete.pdf")
        # print(f"Document deletion successful: {delete_success}")
        
    except Exception as e:
        print(f"Error in example: {str(e)}")
    
    finally:
        # Clean up
        await bot.close()
        print("\nKnowledge Base Bot closed successfully")


def create_sample_config_file():
    """
    Create a sample .env configuration file for the knowledge base bot.
    """
    sample_config = """# Knowledge Base Bot Configuration

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Qdrant Configuration
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=  # Optional, for cloud instances
QDRANT_COLLECTION_NAME=cattle_farming_kb

# Parsing Configuration
PARSING_TIMEOUT_SECONDS=300
PARSING_MAX_FILE_SIZE=104857600  # 100MB

# Chunking Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MIN_CHUNK_LENGTH=100
MAX_CHUNK_LENGTH=2000
ENABLE_ACTIVITY_FILTERING=true

# Vectorization Configuration
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_DIMENSION=1536
VECTORIZATION_BATCH_SIZE=50
VECTORIZATION_MAX_RETRIES=3
VECTORIZATION_REQUESTS_PER_MINUTE=3000

# Vector Store Configuration
VECTOR_SIZE=1536
DISTANCE_METRIC=Cosine
SHARD_NUMBER=1
REPLICATION_FACTOR=1
HNSW_M=16
HNSW_EF_CONSTRUCT=100

# Query Processing Configuration
QUERY_PROCESSING_MODEL=gpt-4o-mini
ENABLE_QUERY_EXPANSION=true
ENABLE_QUERY_REPHRASING=true
ENABLE_FILTER_GENERATION=true
QUERY_PROCESSING_TIMEOUT=30

# Retrieval Configuration
RETRIEVAL_TOP_K=10
RETRIEVAL_SCORE_THRESHOLD=0.7
ENABLE_RERANKING=true
RERANK_TOP_K=20
ENABLE_ACTIVITY_FILTERING=true
FILTER_BOOST=0.1

# Response Generation Configuration
RESPONSE_MODEL=gpt-4o-mini
RESPONSE_TEMPERATURE=0.3
RESPONSE_MAX_TOKENS=1000
MAX_CONTEXT_CHUNKS=5
MAX_HISTORY_MESSAGES=10
RESPONSE_TIMEOUT=60
"""
    
    config_path = Path(".env.example")
    with open(config_path, "w") as f:
        f.write(sample_config)
    
    print(f"Sample configuration file created: {config_path}")
    print("Copy this to .env and update with your actual values")


if __name__ == "__main__":
    print("Knowledge Base Bot Example Usage")
    print("=" * 50)
    
    # Check if .env file exists
    if not Path(".env").exists():
        print("No .env file found. Creating sample configuration...")
        create_sample_config_file()
        print("\nPlease update the .env file with your configuration and run again.")
    else:
        # Run the main example
        asyncio.run(main())
