"""
Vector Store using Qdrant for efficient similarity search and retrieval.

This module handles indexing and retrieval of vectorized document chunks
using Qdrant vector database with support for filtering and metadata.
"""

from typing import List, Dict, Any, Optional, Set
import asyncio
import uuid
from loguru import logger

from qdrant_client import AsyncQdrantClient
from qdrant_client.models import (
    VectorParams, Distance, CollectionInfo, PointStruct,
    Filter, FieldCondition, MatchValue, SearchRequest
)
from qdrant_client.http.exceptions import UnexpectedResponse

from ..config import VectorStoreConfig, ActivityType
from .vectorizer import VectorizedChunk


class VectorStore:
    """
    Vector store using Qdrant for efficient similarity search.
    
    Handles indexing of vectorized chunks with metadata and activity-based
    filtering for improved retrieval performance and relevance.
    """
    
    def __init__(self, config: VectorStoreConfig):
        """
        Initialize the vector store.
        
        Args:
            config: Vector store configuration
        """
        self.config = config
        self.client = None
        self._initialize_client()
        logger.info(f"Vector store initialized for collection: {config.collection_name}")
    
    def _initialize_client(self):
        """Initialize Qdrant client with configuration."""
        try:
            if self.config.api_key:
                # Cloud/remote Qdrant instance
                self.client = AsyncQdrantClient(
                    host=self.config.host,
                    port=self.config.port,
                    api_key=self.config.api_key,
                    https=True
                )
            else:
                # Local Qdrant instance
                self.client = AsyncQdrantClient(
                    host=self.config.host,
                    port=self.config.port
                )
            
            logger.info(f"Qdrant client initialized: {self.config.host}:{self.config.port}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Qdrant client: {str(e)}")
            raise
    
    async def initialize_collection(self) -> bool:
        """
        Initialize the Qdrant collection if it doesn't exist.
        
        Returns:
            bool: True if collection is ready, False otherwise
        """
        try:
            # Check if collection exists
            collections = await self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.config.collection_name not in collection_names:
                logger.info(f"Creating collection: {self.config.collection_name}")
                
                # Create collection with vector configuration
                await self.client.create_collection(
                    collection_name=self.config.collection_name,
                    vectors_config=VectorParams(
                        size=self.config.vector_size,
                        distance=Distance.COSINE if self.config.distance_metric == "Cosine" else Distance.EUCLIDEAN
                    ),
                    shard_number=self.config.shard_number,
                    replication_factor=self.config.replication_factor,
                    hnsw_config={
                        "m": self.config.m,
                        "ef_construct": self.config.ef_construct
                    }
                )
                
                logger.info(f"Collection created successfully: {self.config.collection_name}")
            else:
                logger.info(f"Collection already exists: {self.config.collection_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing collection: {str(e)}")
            return False
    
    async def index_chunks(self, vectorized_chunks: List[VectorizedChunk]) -> bool:
        """
        Index vectorized chunks in the vector store.
        
        Args:
            vectorized_chunks: List of vectorized chunks to index
            
        Returns:
            bool: True if indexing was successful, False otherwise
        """
        if not vectorized_chunks:
            logger.warning("No chunks provided for indexing")
            return True
        
        try:
            # Ensure collection exists
            if not await self.initialize_collection():
                logger.error("Failed to initialize collection")
                return False
            
            logger.info(f"Indexing {len(vectorized_chunks)} chunks")
            
            # Prepare points for insertion
            points = []
            for chunk in vectorized_chunks:
                point_id = str(uuid.uuid4())
                
                # Prepare payload with metadata and activity types
                payload = {
                    "content": chunk.chunk.content,
                    "metadata": chunk.chunk.metadata,
                    "activity_types": list(chunk.chunk.activity_types),
                    "chunk_index": chunk.chunk.chunk_index,
                    "word_count": chunk.chunk.word_count,
                    "embedding_model": chunk.embedding_model,
                    "embedding_timestamp": chunk.embedding_timestamp,
                    "file_name": chunk.chunk.metadata.get("file_name", ""),
                    "file_path": chunk.chunk.metadata.get("file_path", "")
                }
                
                point = PointStruct(
                    id=point_id,
                    vector=chunk.vector,
                    payload=payload
                )
                points.append(point)
            
            # Insert points in batches
            batch_size = 100  # Qdrant recommended batch size
            for i in range(0, len(points), batch_size):
                batch = points[i:i + batch_size]
                
                await self.client.upsert(
                    collection_name=self.config.collection_name,
                    points=batch
                )
                
                logger.info(f"Indexed batch {i//batch_size + 1}/{(len(points) + batch_size - 1)//batch_size}")
            
            logger.info(f"Successfully indexed {len(vectorized_chunks)} chunks")
            return True
            
        except Exception as e:
            logger.error(f"Error indexing chunks: {str(e)}")
            return False
    
    async def search(
        self,
        query_vector: List[float],
        top_k: int = 10,
        score_threshold: float = 0.0,
        activity_filter: Optional[Set[ActivityType]] = None,
        metadata_filter: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar chunks in the vector store.
        
        Args:
            query_vector: Query embedding vector
            top_k: Number of top results to return
            score_threshold: Minimum similarity score threshold
            activity_filter: Filter by activity types
            metadata_filter: Additional metadata filters
            
        Returns:
            List of search results with content and metadata
        """
        try:
            # Build filter conditions
            filter_conditions = []
            
            # Activity type filter
            if activity_filter:
                activity_values = [activity.value for activity in activity_filter]
                filter_conditions.append(
                    FieldCondition(
                        key="activity_types",
                        match=MatchValue(any=activity_values)
                    )
                )
            
            # Metadata filters
            if metadata_filter:
                for key, value in metadata_filter.items():
                    filter_conditions.append(
                        FieldCondition(
                            key=key,
                            match=MatchValue(value=value)
                        )
                    )
            
            # Create filter object
            search_filter = Filter(must=filter_conditions) if filter_conditions else None
            
            # Perform search
            search_results = await self.client.search(
                collection_name=self.config.collection_name,
                query_vector=query_vector,
                limit=top_k,
                score_threshold=score_threshold,
                query_filter=search_filter,
                with_payload=True,
                with_vectors=False  # Don't return vectors to save bandwidth
            )
            
            # Format results
            results = []
            for result in search_results:
                result_data = {
                    "id": result.id,
                    "score": result.score,
                    "content": result.payload.get("content", ""),
                    "metadata": result.payload.get("metadata", {}),
                    "activity_types": result.payload.get("activity_types", []),
                    "chunk_index": result.payload.get("chunk_index", 0),
                    "word_count": result.payload.get("word_count", 0),
                    "file_name": result.payload.get("file_name", ""),
                    "embedding_model": result.payload.get("embedding_model", "")
                }
                results.append(result_data)
            
            logger.info(f"Found {len(results)} results for query")
            return results
            
        except Exception as e:
            logger.error(f"Error searching vector store: {str(e)}")
            return []
    
    async def delete_by_file(self, file_path: str) -> bool:
        """
        Delete all chunks from a specific file.
        
        Args:
            file_path: Path of the file to delete chunks for
            
        Returns:
            bool: True if deletion was successful
        """
        try:
            # Create filter for the file
            file_filter = Filter(
                must=[
                    FieldCondition(
                        key="file_path",
                        match=MatchValue(value=file_path)
                    )
                ]
            )
            
            # Delete points matching the filter
            await self.client.delete(
                collection_name=self.config.collection_name,
                points_selector=file_filter
            )
            
            logger.info(f"Deleted chunks for file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting chunks for file {file_path}: {str(e)}")
            return False
    
    async def get_collection_info(self) -> Optional[Dict[str, Any]]:
        """
        Get information about the collection.
        
        Returns:
            Dict with collection information or None if error
        """
        try:
            info = await self.client.get_collection(self.config.collection_name)
            
            return {
                "name": info.config.params.vectors.size,
                "vector_size": info.config.params.vectors.size,
                "distance_metric": info.config.params.vectors.distance.name,
                "points_count": info.points_count,
                "segments_count": info.segments_count,
                "status": info.status.name
            }
            
        except Exception as e:
            logger.error(f"Error getting collection info: {str(e)}")
            return None
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the vector store.
        
        Returns:
            Dict containing health status
        """
        try:
            # Test connection
            collections = await self.client.get_collections()
            
            # Check if our collection exists
            collection_names = [col.name for col in collections.collections]
            collection_exists = self.config.collection_name in collection_names
            
            # Get collection info if it exists
            collection_info = None
            if collection_exists:
                collection_info = await self.get_collection_info()
            
            return {
                "healthy": True,
                "host": self.config.host,
                "port": self.config.port,
                "collection_name": self.config.collection_name,
                "collection_exists": collection_exists,
                "collection_info": collection_info,
                "total_collections": len(collections.collections)
            }
            
        except Exception as e:
            logger.error(f"Vector store health check failed: {str(e)}")
            return {
                "healthy": False,
                "error": str(e)
            }
    
    async def close(self):
        """Close the vector store connection."""
        try:
            if self.client:
                await self.client.close()
                logger.info("Vector store connection closed")
        except Exception as e:
            logger.error(f"Error closing vector store: {str(e)}")
    
    def __del__(self):
        """Cleanup on deletion."""
        if self.client:
            try:
                asyncio.create_task(self.close())
            except Exception:
                pass  # Ignore errors during cleanup
