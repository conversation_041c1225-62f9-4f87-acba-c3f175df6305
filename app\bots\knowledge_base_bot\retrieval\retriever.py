"""
Retriever for finding relevant document chunks from the vector store.

This module handles the retrieval of relevant document chunks based on
processed queries, with support for filtering, reranking, and relevance scoring.
"""

from typing import List, Dict, Any, Optional, Set
import asyncio
from dataclasses import dataclass
from loguru import logger

from ..config import RetrievalConfig, ActivityType
from ..ingestion.vector_store import VectorStore
from ..ingestion.vectorizer import Vectorizer
from .query_processor import ProcessedQuery


@dataclass
class RetrievedChunk:
    """
    Container for a retrieved document chunk with relevance information.
    """
    content: str
    metadata: Dict[str, Any]
    activity_types: List[str]
    score: float
    chunk_index: int
    file_name: str
    word_count: int
    rerank_score: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "content": self.content,
            "metadata": self.metadata,
            "activity_types": self.activity_types,
            "score": self.score,
            "chunk_index": self.chunk_index,
            "file_name": self.file_name,
            "word_count": self.word_count,
            "rerank_score": self.rerank_score
        }


class Retriever:
    """
    Retriever for finding relevant document chunks from the vector store.
    
    Handles vector similarity search with activity-based filtering,
    result reranking, and relevance scoring for improved retrieval quality.
    """
    
    def __init__(self, vector_store: VectorStore, config: RetrievalConfig):
        """
        Initialize the retriever.
        
        Args:
            vector_store: Vector store instance for searching
            config: Retrieval configuration
        """
        self.vector_store = vector_store
        self.config = config
        self.vectorizer = None  # Will be set when needed
        logger.info("Retriever initialized")
    
    def set_vectorizer(self, vectorizer: Vectorizer):
        """
        Set the vectorizer for query embedding.
        
        Args:
            vectorizer: Vectorizer instance
        """
        self.vectorizer = vectorizer
    
    async def retrieve(
        self,
        processed_query: ProcessedQuery,
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> List[RetrievedChunk]:
        """
        Retrieve relevant chunks for a processed query.
        
        Args:
            processed_query: Processed query with enhancements
            conversation_history: Optional conversation context
            
        Returns:
            List of relevant retrieved chunks
        """
        try:
            logger.info(f"Retrieving chunks for query: {processed_query.original_query}")
            
            # Get query embedding
            if not self.vectorizer:
                logger.error("Vectorizer not set for retriever")
                return []
            
            query_vector = await self.vectorizer.vectorize_query(processed_query.enhanced_query)
            if not query_vector:
                logger.error("Failed to vectorize query")
                return []
            
            # Determine search parameters
            search_top_k = self.config.rerank_top_k if self.config.enable_reranking else self.config.top_k
            
            # Perform vector search
            search_results = await self.vector_store.search(
                query_vector=query_vector,
                top_k=search_top_k,
                score_threshold=self.config.score_threshold,
                activity_filter=processed_query.activity_filters if self.config.enable_activity_filtering else None
            )
            
            if not search_results:
                logger.info("No results found for query")
                return []
            
            # Convert to RetrievedChunk objects
            retrieved_chunks = []
            for result in search_results:
                chunk = RetrievedChunk(
                    content=result["content"],
                    metadata=result["metadata"],
                    activity_types=result["activity_types"],
                    score=result["score"],
                    chunk_index=result["chunk_index"],
                    file_name=result["file_name"],
                    word_count=result["word_count"]
                )
                retrieved_chunks.append(chunk)
            
            # Apply activity filtering boost if enabled
            if self.config.enable_activity_filtering and processed_query.activity_filters:
                retrieved_chunks = self._apply_activity_boost(retrieved_chunks, processed_query.activity_filters)
            
            # Rerank results if enabled
            if self.config.enable_reranking and len(retrieved_chunks) > self.config.top_k:
                retrieved_chunks = await self._rerank_results(
                    retrieved_chunks, processed_query, conversation_history
                )
            
            # Limit to top_k results
            final_results = retrieved_chunks[:self.config.top_k]
            
            logger.info(f"Retrieved {len(final_results)} relevant chunks")
            return final_results
            
        except Exception as e:
            logger.error(f"Error retrieving chunks: {str(e)}")
            return []
    
    def _apply_activity_boost(
        self,
        chunks: List[RetrievedChunk],
        query_activities: Set[ActivityType]
    ) -> List[RetrievedChunk]:
        """
        Apply score boost for chunks matching query activity types.
        
        Args:
            chunks: List of retrieved chunks
            query_activities: Activity types from query
            
        Returns:
            List of chunks with boosted scores
        """
        query_activity_values = {activity.value for activity in query_activities}
        
        for chunk in chunks:
            chunk_activities = set(chunk.activity_types)
            
            # Check for activity overlap
            if chunk_activities.intersection(query_activity_values):
                # Apply boost to score
                chunk.score = min(1.0, chunk.score + self.config.filter_boost)
        
        # Re-sort by score
        chunks.sort(key=lambda x: x.score, reverse=True)
        return chunks
    
    async def _rerank_results(
        self,
        chunks: List[RetrievedChunk],
        processed_query: ProcessedQuery,
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> List[RetrievedChunk]:
        """
        Rerank results using additional relevance signals.
        
        Args:
            chunks: Initial retrieved chunks
            processed_query: Processed query information
            conversation_history: Conversation context
            
        Returns:
            Reranked list of chunks
        """
        try:
            # Simple reranking based on multiple factors
            for chunk in chunks:
                rerank_score = chunk.score
                
                # Boost for keyword matches
                content_lower = chunk.content.lower()
                keyword_matches = sum(1 for keyword in processed_query.keywords 
                                    if keyword.lower() in content_lower)
                if keyword_matches > 0:
                    rerank_score += 0.1 * (keyword_matches / len(processed_query.keywords))
                
                # Boost for query term matches
                query_terms = processed_query.enhanced_query.lower().split()
                term_matches = sum(1 for term in query_terms 
                                 if len(term) > 3 and term in content_lower)
                if term_matches > 0:
                    rerank_score += 0.05 * (term_matches / len(query_terms))
                
                # Boost for recent conversation context
                if conversation_history:
                    recent_context = " ".join([
                        exchange.get("content", "").lower() 
                        for exchange in conversation_history[-2:]
                        if exchange.get("role") == "user"
                    ])
                    
                    if recent_context:
                        context_terms = recent_context.split()
                        context_matches = sum(1 for term in context_terms 
                                            if len(term) > 3 and term in content_lower)
                        if context_matches > 0:
                            rerank_score += 0.03 * (context_matches / len(context_terms))
                
                # Penalize very short chunks
                if chunk.word_count < 50:
                    rerank_score *= 0.9
                
                # Boost for medium-length chunks (good balance of detail and focus)
                elif 100 <= chunk.word_count <= 300:
                    rerank_score *= 1.05
                
                chunk.rerank_score = min(1.0, rerank_score)
            
            # Sort by rerank score
            chunks.sort(key=lambda x: x.rerank_score or x.score, reverse=True)
            
            logger.info("Results reranked successfully")
            return chunks
            
        except Exception as e:
            logger.error(f"Error reranking results: {str(e)}")
            # Return original order on error
            return chunks
    
    async def retrieve_by_file(
        self,
        file_name: str,
        query_vector: Optional[List[float]] = None,
        top_k: int = 10
    ) -> List[RetrievedChunk]:
        """
        Retrieve chunks from a specific file.
        
        Args:
            file_name: Name of the file to retrieve from
            query_vector: Optional query vector for similarity ranking
            top_k: Number of chunks to retrieve
            
        Returns:
            List of chunks from the specified file
        """
        try:
            # Search with file filter
            metadata_filter = {"file_name": file_name}
            
            search_results = await self.vector_store.search(
                query_vector=query_vector or [0.0] * self.vector_store.config.vector_size,
                top_k=top_k,
                score_threshold=0.0,  # Get all chunks from file
                metadata_filter=metadata_filter
            )
            
            # Convert to RetrievedChunk objects
            retrieved_chunks = []
            for result in search_results:
                chunk = RetrievedChunk(
                    content=result["content"],
                    metadata=result["metadata"],
                    activity_types=result["activity_types"],
                    score=result["score"],
                    chunk_index=result["chunk_index"],
                    file_name=result["file_name"],
                    word_count=result["word_count"]
                )
                retrieved_chunks.append(chunk)
            
            return retrieved_chunks
            
        except Exception as e:
            logger.error(f"Error retrieving chunks from file {file_name}: {str(e)}")
            return []
    
    async def get_similar_chunks(
        self,
        reference_chunk: RetrievedChunk,
        top_k: int = 5,
        exclude_same_file: bool = False
    ) -> List[RetrievedChunk]:
        """
        Find chunks similar to a reference chunk.
        
        Args:
            reference_chunk: Reference chunk to find similar content for
            top_k: Number of similar chunks to retrieve
            exclude_same_file: Whether to exclude chunks from the same file
            
        Returns:
            List of similar chunks
        """
        try:
            # Vectorize the reference chunk content
            if not self.vectorizer:
                logger.error("Vectorizer not set for retriever")
                return []
            
            reference_vector = await self.vectorizer.vectorize_single_text(reference_chunk.content)
            if not reference_vector:
                logger.error("Failed to vectorize reference chunk")
                return []
            
            # Search for similar chunks
            metadata_filter = None
            if exclude_same_file:
                # This would require a "not equals" filter, which might not be directly supported
                # For now, we'll filter after retrieval
                pass
            
            search_results = await self.vector_store.search(
                query_vector=reference_vector,
                top_k=top_k + (5 if exclude_same_file else 0),  # Get extra if filtering
                score_threshold=0.5  # Reasonable similarity threshold
            )
            
            # Convert and filter results
            similar_chunks = []
            for result in search_results:
                # Skip the reference chunk itself (if it exists in the store)
                if (result["content"] == reference_chunk.content and 
                    result["chunk_index"] == reference_chunk.chunk_index):
                    continue
                
                # Skip same file if requested
                if exclude_same_file and result["file_name"] == reference_chunk.file_name:
                    continue
                
                chunk = RetrievedChunk(
                    content=result["content"],
                    metadata=result["metadata"],
                    activity_types=result["activity_types"],
                    score=result["score"],
                    chunk_index=result["chunk_index"],
                    file_name=result["file_name"],
                    word_count=result["word_count"]
                )
                similar_chunks.append(chunk)
                
                if len(similar_chunks) >= top_k:
                    break
            
            return similar_chunks
            
        except Exception as e:
            logger.error(f"Error finding similar chunks: {str(e)}")
            return []
    
    def get_retrieval_stats(self, chunks: List[RetrievedChunk]) -> Dict[str, Any]:
        """
        Get statistics about retrieved chunks.
        
        Args:
            chunks: List of retrieved chunks
            
        Returns:
            Dict with retrieval statistics
        """
        if not chunks:
            return {"total_chunks": 0}
        
        # Calculate statistics
        scores = [chunk.score for chunk in chunks]
        word_counts = [chunk.word_count for chunk in chunks]
        
        # Activity type distribution
        activity_counts = {}
        for chunk in chunks:
            for activity in chunk.activity_types:
                activity_counts[activity] = activity_counts.get(activity, 0) + 1
        
        # File distribution
        file_counts = {}
        for chunk in chunks:
            file_counts[chunk.file_name] = file_counts.get(chunk.file_name, 0) + 1
        
        return {
            "total_chunks": len(chunks),
            "avg_score": sum(scores) / len(scores),
            "min_score": min(scores),
            "max_score": max(scores),
            "avg_word_count": sum(word_counts) / len(word_counts),
            "activity_distribution": activity_counts,
            "file_distribution": file_counts,
            "reranked_chunks": sum(1 for chunk in chunks if chunk.rerank_score is not None)
        }
